import 'dart:io';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../models/user_profile_model.dart';
import '../services/user_profile_service.dart';
import '../constants/font_sizes.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  Future<UserProfile>? _profileFuture;
  final TextEditingController _headerTextController = TextEditingController();
  final TextEditingController _doctorNameEnController = TextEditingController();
  final TextEditingController _doctorNameArController = TextEditingController();
  final TextEditingController _doctorDegreeEnController =
      TextEditingController();
  final TextEditingController _doctorDegreeArController =
      TextEditingController();
  final TextEditingController _doctorLocationEnController =
      TextEditingController();
  final TextEditingController _doctorLocationArController =
      TextEditingController();
  final TextEditingController _doctorSpecializationEnController =
      TextEditingController();
  final TextEditingController _doctorSpecializationArController =
      TextEditingController();

  File? _selectedLogoImage;
  File? _selectedBackgroundImage;
  final Map<String, File> _positionedBackgroundImages = {};
  final Map<String, Map<String, dynamic>> _positionedBackgroundColors = {};
  final Map<String, double> _positionedBackgroundImageTransparency = {};
  bool _isLoading = false;

  // Profile slot management
  int _selectedProfileSlot = 1;
  List<bool> _profileSlotsHaveData = [false, false, false, false, false];

  Color _reportTitleColor = Colors.black;
  Color _doctorTitleColor = Colors.black;
  Color _patientInfoTextColor = Colors.black;
  Color _echoParametersTextColor = Colors.black;
  Color _interpretationTextColor = Colors.black;
  Color _conclusionTextColor = Colors.black;
  Color _footerTextColor = Colors.black;

  double _pdfBackgroundTransparency = 1.0;

  static const Color defaultReportTitleColor = Colors.black;
  static const Color defaultDoctorTitleColor = Colors.black;
  static const Color defaultPatientInfoTextColor = Colors.black;
  static const Color defaultEchoParametersTextColor = Colors.black;
  static const Color defaultInterpretationTextColor = Colors.black;
  static const Color defaultConclusionTextColor = Colors.black;
  static const Color defaultFooterTextColor = Colors.black;

  final List<String> _positionedPositions = [
    'top-left',
    'top-right',
    'bottom-left',
    'bottom-right',
  ];

  final List<String> _sectionPositions = [
    'header',
    'patient-data',
    'echo-parameters',
    'interpretation',
    'footer',
  ];

  List<String> get _positions => [
    ..._positionedPositions,
    ..._sectionPositions,
  ];

  @override
  void initState() {
    super.initState();

    _initializeDefaultColors();
    _profileFuture = _initializeCurrentSlot();
    _loadProfileSlotsStatus();
  }

  void _initializeDefaultColors() {
    _reportTitleColor = defaultReportTitleColor;
    _doctorTitleColor = defaultDoctorTitleColor;
    _patientInfoTextColor = defaultPatientInfoTextColor;
    _echoParametersTextColor = defaultEchoParametersTextColor;
    _interpretationTextColor = defaultInterpretationTextColor;
    _conclusionTextColor = defaultConclusionTextColor;
    _footerTextColor = defaultFooterTextColor;
  }

  @override
  void dispose() {
    _headerTextController.dispose();
    _doctorNameEnController.dispose();
    _doctorNameArController.dispose();
    _doctorDegreeEnController.dispose();
    _doctorDegreeArController.dispose();
    _doctorLocationEnController.dispose();
    _doctorLocationArController.dispose();
    _doctorSpecializationEnController.dispose();
    _doctorSpecializationArController.dispose();
    super.dispose();
  }

  Future<UserProfile> _initializeCurrentSlot() async {
    try {
      final currentSlot = await UserProfileService.getCurrentProfileSlot();
      setState(() {
        _selectedProfileSlot = currentSlot;
      });

      final profile = await _loadUserProfileFromSlotNumber(currentSlot);
      return profile;
    } catch (e) {
      debugPrint('Error initializing current slot: $e');
      // Fallback to slot 1
      setState(() {
        _selectedProfileSlot = 1;
      });
      final profile = await _loadUserProfileFromSlotNumber(1);
      return profile;
    }
  }

  Future<UserProfile> _loadUserProfileFromSlotNumber(int slotNumber) async {
    _selectedLogoImage = null;
    _selectedBackgroundImage = null;
    _positionedBackgroundImages.clear();

    final profile = await UserProfileService.getUserProfileFromSlot(slotNumber);

    _headerTextController.text = profile.headerText;
    _doctorNameEnController.text = profile.doctorNameEn;
    _doctorNameArController.text = profile.doctorNameAr;
    _doctorDegreeEnController.text = profile.doctorDegreeEn;
    _doctorDegreeArController.text = profile.doctorDegreeAr;
    _doctorLocationEnController.text = profile.doctorLocationEn;
    _doctorLocationArController.text = profile.doctorLocationAr;
    _doctorSpecializationEnController.text = profile.doctorSpecializationEn;
    _doctorSpecializationArController.text = profile.doctorSpecializationAr;

    setState(() {
      if (profile.reportTitleColor.isNotEmpty) {
        _reportTitleColor = _hexToColor(profile.reportTitleColor);
      } else {
        _reportTitleColor = defaultReportTitleColor;
      }

      if (profile.doctorTitleColor.isNotEmpty) {
        _doctorTitleColor = _hexToColor(profile.doctorTitleColor);
      } else {
        _doctorTitleColor = defaultDoctorTitleColor;
      }

      if (profile.patientInfoTextColor.isNotEmpty) {
        _patientInfoTextColor = _hexToColor(profile.patientInfoTextColor);
      } else {
        _patientInfoTextColor = defaultPatientInfoTextColor;
      }

      if (profile.echoParametersTextColor.isNotEmpty) {
        _echoParametersTextColor = _hexToColor(profile.echoParametersTextColor);
      } else {
        _echoParametersTextColor = defaultEchoParametersTextColor;
      }

      if (profile.interpretationTextColor.isNotEmpty) {
        _interpretationTextColor = _hexToColor(profile.interpretationTextColor);
      } else {
        _interpretationTextColor = defaultInterpretationTextColor;
      }

      if (profile.conclusionTextColor.isNotEmpty) {
        _conclusionTextColor = _hexToColor(profile.conclusionTextColor);
      } else {
        _conclusionTextColor = defaultConclusionTextColor;
      }

      if (profile.footerTextColor.isNotEmpty) {
        _footerTextColor = _hexToColor(profile.footerTextColor);
      } else {
        _footerTextColor = defaultFooterTextColor;
      }

      _pdfBackgroundTransparency = profile.pdfBackgroundTransparency;
    });

    if (profile.logoImagePath != null) {
      final logoFile = await UserProfileService.getLogoImageFile();
      if (logoFile != null) {
        _selectedLogoImage = logoFile;
      }
    }

    if (profile.backgroundImagePath != null) {
      final backgroundFile = await UserProfileService.getBackgroundImageFile();
      if (backgroundFile != null) {
        _selectedBackgroundImage = backgroundFile;
      }
    }

    _positionedBackgroundImages.clear();
    _positionedBackgroundColors.clear();
    _positionedBackgroundImageTransparency.clear();

    for (final bgImage in profile.backgroundImages) {
      if (bgImage.position == 'center') {
        continue;
      }

      if (bgImage.type == 'color') {
        _positionedBackgroundColors[bgImage.position] = {
          'color': bgImage.color,
          'alpha': bgImage.alpha,
        };
        continue;
      }

      final file = await UserProfileService.getBackgroundImageFileByPath(
        bgImage.path,
      );
      if (file != null) {
        _positionedBackgroundImages[bgImage.position] = file;
        _positionedBackgroundImageTransparency[bgImage.position] =
            bgImage.alpha;
      }
    }

    return profile;
  }

  // Profile slot management methods
  Future<void> _loadProfileSlotsStatus() async {
    try {
      final slotsStatus = await UserProfileService.getProfileSlotsStatus();
      setState(() {
        _profileSlotsHaveData = slotsStatus;
      });
    } catch (e) {
      debugPrint('Error loading profile slots status: $e');
    }
  }

  Future<void> _switchToProfileSlot(int slotNumber) async {
    if (slotNumber < 1 || slotNumber > 5) return;

    setState(() {
      _isLoading = true;
      _selectedProfileSlot = slotNumber;
    });

    try {
      // Set this as the current active profile slot globally
      await UserProfileService.setCurrentProfileSlot(slotNumber);

      final profile = await _loadUserProfileFromSlotNumber(slotNumber);

      setState(() {
        _profileFuture = Future.value(profile);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Loaded Profile $slotNumber'),
            duration: const Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading profile $slotNumber: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickLogoImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        setState(() {
          _selectedLogoImage = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error selecting image: $e')));
      }
    }
  }

  Future<void> _pickBackgroundImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        setState(() {
          _selectedBackgroundImage = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error selecting background image: $e')),
        );
      }
    }
  }

  Future<void> _pickPositionedBackgroundImage(String position) async {
    if (!mounted) return;

    final String? choice = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Choose Background Type for ${position.replaceAll('-', ' ').split(' ').map((word) => word[0].toUpperCase() + word.substring(1)).join(' ')}',
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(FontAwesomeIcons.image),
                title: const Text('Image'),
                subtitle: const Text('Select an image from gallery'),
                onTap: () => Navigator.of(context).pop('image'),
              ),
              ListTile(
                leading: const Icon(FontAwesomeIcons.fillDrip),
                title: const Text('Solid Color'),
                subtitle: const Text('Choose a color with transparency'),
                onTap: () => Navigator.of(context).pop('color'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );

    if (choice == null) return;

    if (choice == 'image') {
      try {
        final ImagePicker picker = ImagePicker();
        final XFile? image = await picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1200,
          maxHeight: 1200,
          imageQuality: 85,
        );

        if (image != null && mounted) {
          setState(() {
            _positionedBackgroundImages[position] = File(image.path);
            _positionedBackgroundImageTransparency[position] =
                1.0; // Default to fully opaque

            _positionedBackgroundColors.remove(position);
          });
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Error selecting background image for $position: $e',
              ),
            ),
          );
        }
      }
    } else if (choice == 'color') {
      await _showColorPickerWithAlpha(
        'Background Color for ${position.replaceAll('-', ' ').split(' ').map((word) => word[0].toUpperCase() + word.substring(1)).join(' ')}',
        position,
      );

      if (mounted) {
        setState(() {
          debugPrint(
            'Refreshing UI after color picker for position: $position',
          );
        });
      }
    }
  }

  Future<void> _showColorPickerWithAlpha(String title, String position) async {
    Color pickerColor = Color.fromRGBO(30, 144, 255, 0.7);

    if (_positionedBackgroundColors.containsKey(position)) {
      final colorData = _positionedBackgroundColors[position]!;
      final String colorHex = colorData['color'] as String;
      final double alpha = colorData['alpha'] as double;

      try {
        if (colorHex.length == 6) {
          final int r = int.parse(colorHex.substring(0, 2), radix: 16);
          final int g = int.parse(colorHex.substring(2, 4), radix: 16);
          final int b = int.parse(colorHex.substring(4, 6), radix: 16);
          final int alphaValue = (alpha * 255).round();

          pickerColor = Color.fromARGB(alphaValue, r, g, b);

          debugPrint(
            'Loaded background color for $position: r=$r, g=$g, b=$b, alphaValue=$alphaValue, alpha=$alpha',
          );

          if (alphaValue < 10) {
            debugPrint(
              'Alpha value too low, increasing for visibility in UI only',
            );

            pickerColor = Color.fromARGB(50, r, g, b);
          }
        } else {
          pickerColor = Color.fromARGB((alpha * 255).round(), 30, 144, 255);
          debugPrint(
            'Invalid hex color format for $position: $colorHex, using default blue',
          );
        }
      } catch (e) {
        pickerColor = Color.fromARGB((alpha * 255).round(), 30, 144, 255);
        debugPrint('Error parsing color for $position: $e, using default blue');
      }
    }

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: Text(
                title,
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Select Color',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),

                    const SizedBox(height: 12),

                    ColorPicker(
                      pickerColor: pickerColor,
                      onColorChanged: (Color color) {
                        setState(() {
                          pickerColor = color;
                        });
                      },
                      pickerAreaHeightPercent: 0.7,
                      enableAlpha: true,
                      displayThumbColor: false,

                      labelTypes: const [],
                      paletteType: PaletteType.hsv,
                      portraitOnly: true,

                      colorPickerWidth: 280,
                      pickerAreaBorderRadius: const BorderRadius.all(
                        Radius.circular(8),
                      ),
                    ),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  onPressed: () async {
                    if (mounted) {
                      setState(() {
                        final int r = (pickerColor.r * 255).round();
                        final int g = (pickerColor.g * 255).round();
                        final int b = (pickerColor.b * 255).round();
                        final int a = (pickerColor.a * 255).round();

                        final String rHex = r.toRadixString(16).padLeft(2, '0');
                        final String gHex = g.toRadixString(16).padLeft(2, '0');
                        final String bHex = b.toRadixString(16).padLeft(2, '0');
                        final String colorHex = rHex + gHex + bHex;

                        final double alpha = a / 255.0;

                        debugPrint(
                          'Saving color with alpha: r=$r ($rHex), g=$g ($gHex), b=$b ($bHex), a=$a, alpha=$alpha, hex=$colorHex',
                        );

                        if (_positionedBackgroundImages.containsKey(position)) {
                          debugPrint(
                            'Removing existing image for position: $position',
                          );
                          _positionedBackgroundImages.remove(position);
                        }

                        _positionedBackgroundColors[position] = {
                          'color': colorHex,
                          'alpha': alpha,
                        };

                        debugPrint(
                          'Stored color in _positionedBackgroundColors for position: $position',
                        );
                      });

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Color saved for ${position.replaceAll('-', ' ')}',
                          ),
                          duration: const Duration(seconds: 1),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );

                      Navigator.of(context).pop();
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                  child: const Text(
                    'Save Color',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _saveProfile(UserProfile currentProfile) async {
    setState(() {
      _isLoading = true;
    });

    try {
      String? logoPath;
      if (_selectedLogoImage != null) {
        if (currentProfile.logoImagePath != _selectedLogoImage!.path) {
          logoPath = await UserProfileService.saveLogoImage(
            _selectedLogoImage!,
          );
        } else {
          logoPath = currentProfile.logoImagePath;
        }
      } else {
        logoPath = null;
      }

      String? backgroundPath;
      if (_selectedBackgroundImage != null) {
        if (currentProfile.backgroundImagePath !=
            _selectedBackgroundImage!.path) {
          backgroundPath = await UserProfileService.saveBackgroundImage(
            _selectedBackgroundImage!,
          );
        } else {
          backgroundPath = currentProfile.backgroundImagePath;
        }
      } else {
        backgroundPath = null;
      }

      List<BackgroundImage> backgroundImages = [];

      Set<String> removedPositions = Set.from(_positions);

      for (final position in _positions) {
        if (position == 'center') {
          continue;
        }

        if (_positionedBackgroundImages.containsKey(position)) {
          removedPositions.remove(position);

          final imageFile = _positionedBackgroundImages[position]!;

          bool isExistingImage = false;
          String? imagePath;

          for (final bgImage in currentProfile.backgroundImages) {
            if (bgImage.position == position &&
                bgImage.path == imageFile.path &&
                bgImage.type == 'image') {
              isExistingImage = true;
              imagePath = bgImage.path;
              break;
            }
          }

          if (!isExistingImage) {
            imagePath = await UserProfileService.savePositionedBackgroundImage(
              imageFile,
              position,
            );
          }

          if (imagePath != null) {
            final double transparency =
                _positionedBackgroundImageTransparency[position] ?? 1.0;
            backgroundImages.add(
              BackgroundImage(
                path: imagePath,
                position: position,
                type: 'image',
                alpha: transparency,
              ),
            );
          }
        } else if (_positionedBackgroundColors.containsKey(position)) {
          removedPositions.remove(position);

          final colorData = _positionedBackgroundColors[position]!;
          final String colorHex = colorData['color'] as String;
          final double alpha = colorData['alpha'] as double;

          final String colorPath = 'color_$position';

          backgroundImages.add(
            BackgroundImage(
              path: colorPath,
              position: position,
              type: 'color',
              color: colorHex,
              alpha: alpha,
            ),
          );
        }
      }

      for (final position in removedPositions) {
        for (final bgImage in currentProfile.backgroundImages) {
          if (bgImage.position == position) {
            break;
          }
        }
      }

      final updatedProfile = UserProfile(
        languageCode: currentProfile.languageCode,
        logoImagePath: logoPath,
        backgroundImagePath: backgroundPath,
        backgroundImages: backgroundImages,
        useArabicHeader: currentProfile.useArabicHeader,
        useArabicLeftHeader: currentProfile.useArabicLeftHeader,
        headerText: _headerTextController.text,
        doctorNameEn: _doctorNameEnController.text,
        doctorNameAr: _doctorNameArController.text,
        doctorDegreeEn: _doctorDegreeEnController.text,
        doctorDegreeAr: _doctorDegreeArController.text,
        doctorLocationEn: _doctorLocationEnController.text,
        doctorLocationAr: _doctorLocationArController.text,
        doctorSpecializationEn: _doctorSpecializationEnController.text,
        doctorSpecializationAr: _doctorSpecializationArController.text,
        reportTitleColor: _colorToHex(_reportTitleColor),
        doctorTitleColor: _colorToHex(_doctorTitleColor),
        patientInfoTextColor: _colorToHex(_patientInfoTextColor),
        echoParametersTextColor: _colorToHex(_echoParametersTextColor),
        interpretationTextColor: _colorToHex(_interpretationTextColor),
        conclusionTextColor: _colorToHex(_conclusionTextColor),
        footerTextColor: _colorToHex(_footerTextColor),
        pdfBackgroundTransparency: _pdfBackgroundTransparency,
      );

      for (final _ in backgroundImages) {}

      UserProfileService.clearCachedProfile();
      UserProfileService.clearCachedProfileSlots();

      final success = await UserProfileService.saveUserProfileToSlot(
        updatedProfile,
        _selectedProfileSlot,
      );

      if (success) {
        // Set this as the current active profile slot
        await UserProfileService.setCurrentProfileSlot(_selectedProfileSlot);

        // Update the profile slots status
        await _loadProfileSlotsStatus();

        UserProfileService.clearCachedProfile();
        UserProfileService.clearCachedProfileSlots();

        final updatedProfileFromDisk =
            await UserProfileService.getUserProfileFromSlot(
              _selectedProfileSlot,
            );

        setState(() {
          _profileFuture = Future.value(updatedProfileFromDisk);

          if (updatedProfileFromDisk.reportTitleColor.isNotEmpty) {
            _reportTitleColor = _hexToColor(
              updatedProfileFromDisk.reportTitleColor,
            );
          } else {
            _reportTitleColor = defaultReportTitleColor;
          }

          if (updatedProfileFromDisk.doctorTitleColor.isNotEmpty) {
            _doctorTitleColor = _hexToColor(
              updatedProfileFromDisk.doctorTitleColor,
            );
          } else {
            _doctorTitleColor = defaultDoctorTitleColor;
          }

          if (updatedProfileFromDisk.patientInfoTextColor.isNotEmpty) {
            _patientInfoTextColor = _hexToColor(
              updatedProfileFromDisk.patientInfoTextColor,
            );
          } else {
            _patientInfoTextColor = defaultPatientInfoTextColor;
          }

          if (updatedProfileFromDisk.echoParametersTextColor.isNotEmpty) {
            _echoParametersTextColor = _hexToColor(
              updatedProfileFromDisk.echoParametersTextColor,
            );
          } else {
            _echoParametersTextColor = defaultEchoParametersTextColor;
          }

          if (updatedProfileFromDisk.interpretationTextColor.isNotEmpty) {
            _interpretationTextColor = _hexToColor(
              updatedProfileFromDisk.interpretationTextColor,
            );
          } else {
            _interpretationTextColor = defaultInterpretationTextColor;
          }

          if (updatedProfileFromDisk.conclusionTextColor.isNotEmpty) {
            _conclusionTextColor = _hexToColor(
              updatedProfileFromDisk.conclusionTextColor,
            );
          } else {
            _conclusionTextColor = defaultConclusionTextColor;
          }

          if (updatedProfileFromDisk.footerTextColor.isNotEmpty) {
            _footerTextColor = _hexToColor(
              updatedProfileFromDisk.footerTextColor,
            );
          } else {
            _footerTextColor = defaultFooterTextColor;
          }
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Profile $_selectedProfileSlot saved successfully'),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to save profile')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error saving profile: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: const Color(0xFF1E88E5),
        foregroundColor: Colors.white,
        elevation: 4,
        centerTitle: true,
      ),
      body: FutureBuilder<UserProfile>(
        future: _profileFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Text(
                'Error loading profile: ${snapshot.error}',
                style: const TextStyle(color: Colors.red),
              ),
            );
          }

          final profile = snapshot.data ?? UserProfile.defaultProfile;

          return Container(
            color: Colors.grey.shade50,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildProfileSlotButtons(),

                  const SizedBox(height: 14),

                  _buildSectionHeader(context, 'PDF Header Customization'),

                  _buildHeaderPreview(profile),

                  const SizedBox(height: 14),

                  _buildSectionHeader(context, 'Left Header Information'),
                  _buildLanguageSelection(
                    profile,
                    true,
                  ), // Pass true for left section
                  if (profile.useArabicLeftHeader) ...[
                    _buildTextField(
                      _doctorNameArController,
                      'Doctor Name (Arabic)',
                      'Enter your name in Arabic',
                    ),
                    const SizedBox(height: 14),
                    _buildTextField(
                      _doctorDegreeArController,
                      'Credentials (Arabic)',
                      'Enter your credentials in Arabic',
                    ),
                    const SizedBox(height: 14),
                    _buildTextField(
                      _doctorLocationArController,
                      'Location/Department (Arabic)',
                      'Enter your location or department in Arabic',
                    ),
                    const SizedBox(height: 14),
                    _buildTextField(
                      _doctorSpecializationArController,
                      'Specialization (Arabic)',
                      'Enter your specialization in Arabic',
                    ),
                  ] else ...[
                    _buildTextField(
                      _doctorNameEnController,
                      'Doctor Name (English)',
                      'Enter your name in English',
                    ),
                    const SizedBox(height: 14),
                    _buildTextField(
                      _doctorDegreeEnController,
                      'Credentials (English)',
                      'Enter your credentials in English (e.g., MD, FACC)',
                    ),
                    const SizedBox(height: 14),
                    _buildTextField(
                      _doctorLocationEnController,
                      'Location/Department (English)',
                      'Enter your location or department in English',
                    ),
                    const SizedBox(height: 14),
                    _buildTextField(
                      _doctorSpecializationEnController,
                      'Specialization (English)',
                      'Enter your specialization in English (e.g., Cardiology, Radiology)',
                    ),
                  ],
                  const SizedBox(height: 14),

                  const SizedBox(height: 14),

                  _buildSectionHeader(context, 'Right Header Information'),
                  _buildLanguageSelection(
                    profile,
                    false,
                  ), // Pass false for right section
                  if (profile.useArabicHeader) ...[
                    _buildTextField(
                      _doctorNameArController,
                      'Doctor Name (Arabic)',
                      'Enter your name in Arabic',
                    ),
                    const SizedBox(height: 14),
                    _buildTextField(
                      _doctorDegreeArController,
                      'Credentials (Arabic)',
                      'Enter your credentials in Arabic',
                    ),
                    const SizedBox(height: 14),
                    _buildTextField(
                      _doctorLocationArController,
                      'Location/Department (Arabic)',
                      'Enter your location or department in Arabic',
                    ),
                    const SizedBox(height: 14),
                    _buildTextField(
                      _doctorSpecializationArController,
                      'Specialization (Arabic)',
                      'Enter your specialization in Arabic',
                    ),
                  ] else ...[
                    _buildTextField(
                      _headerTextController,
                      'Right Header Text (Line 1)',
                      'Enter text for the first line on the right side',
                    ),
                    const SizedBox(height: 14),
                    _buildTextField(
                      _doctorDegreeArController,
                      'Right Header Text (Line 2)',
                      'Enter text for the second line on the right side',
                    ),
                    const SizedBox(height: 14),
                    _buildTextField(
                      _doctorLocationArController,
                      'Right Header Text (Line 3)',
                      'Enter text for the third line on the right side',
                    ),
                  ],

                  const SizedBox(height: 24),

                  _buildLogoSelection(),

                  const SizedBox(height: 16),

                  _buildBackgroundSelection(),

                  const SizedBox(height: 24),

                  _buildSectionHeader(context, 'Text Color Customization'),

                  _buildTextColorSelection(),

                  const SizedBox(height: 24),

                  _buildSaveButton(profile),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: FontSizes.heading2,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildHeaderPreview(UserProfile profile) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(80),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(top: 5.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if (profile.useArabicLeftHeader) ...[
                    Text(
                      _doctorNameArController.text.isNotEmpty
                          ? _doctorNameArController.text
                          : profile.doctorNameAr,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontWeight: FontWeight.bold,
                        fontSize: FontSizes.bodyMedium,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _doctorDegreeArController.text.isNotEmpty
                          ? _doctorDegreeArController.text
                          : profile.doctorDegreeAr,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: FontSizes.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _doctorLocationArController.text.isNotEmpty
                          ? _doctorLocationArController.text
                          : profile.doctorLocationAr,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: FontSizes.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _doctorSpecializationArController.text.isNotEmpty
                          ? _doctorSpecializationArController.text
                          : profile.doctorSpecializationAr,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: FontSizes.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ] else ...[
                    Text(
                      profile.doctorNameEn,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontWeight: FontWeight.bold,
                        fontSize: FontSizes.bodyMedium,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      profile.doctorDegreeEn,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: FontSizes.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      profile.doctorLocationEn,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: FontSizes.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      profile.doctorSpecializationEn,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: FontSizes.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
          ),

          Expanded(
            child: Center(
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.rectangle,
                  borderRadius: BorderRadius.circular(4),
                ),
                child:
                    _selectedLogoImage != null
                        ? Image.file(_selectedLogoImage!, fit: BoxFit.contain)
                        : const Icon(
                          FontAwesomeIcons.heartPulse,
                          color: Colors.blue,
                          size: 30,
                        ),
              ),
            ),
          ),

          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(top: 5.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if (profile.useArabicHeader) ...[
                    Text(
                      _doctorNameArController.text.isNotEmpty
                          ? _doctorNameArController.text
                          : profile.doctorNameAr,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontWeight: FontWeight.bold,
                        fontSize: FontSizes.bodyMedium,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _doctorDegreeArController.text.isNotEmpty
                          ? _doctorDegreeArController.text
                          : profile.doctorDegreeAr,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: FontSizes.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _doctorLocationArController.text.isNotEmpty
                          ? _doctorLocationArController.text
                          : profile.doctorLocationAr,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: FontSizes.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _doctorSpecializationArController.text.isNotEmpty
                          ? _doctorSpecializationArController.text
                          : profile.doctorSpecializationAr,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: FontSizes.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ] else ...[
                    Text(
                      _headerTextController.text,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontWeight: FontWeight.bold,
                        fontSize: FontSizes.bodyMedium,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _doctorDegreeArController.text,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: FontSizes.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _doctorLocationArController.text,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: FontSizes.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogoSelection() {
    return Card(
      elevation: 10,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      clipBehavior: Clip.antiAlias,
      color: Colors.white,
      shadowColor: Colors.black.withAlpha(80),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      child: ListTile(
        leading: Icon(
          FontAwesomeIcons.image,
          color: Theme.of(context).primaryColor,
        ),
        title: const Text('Your Logo'),
        subtitle: Text(
          _selectedLogoImage != null
              ? 'Logo image selected'
              : 'Select a logo for the header',
        ),
        trailing:
            _selectedLogoImage != null
                ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ClipOval(
                      child: SizedBox(
                        width: 40,
                        height: 40,
                        child: Image.file(
                          _selectedLogoImage!,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () {
                        setState(() {
                          _selectedLogoImage = null;
                        });
                      },
                    ),
                  ],
                )
                : const Icon(FontAwesomeIcons.plus),
        onTap: _pickLogoImage,
      ),
    );
  }

  Widget _buildBackgroundSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          elevation: 10,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          clipBehavior: Clip.antiAlias,
          color: Colors.white,
          shadowColor: Colors.black.withAlpha(80),
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
          child: ListTile(
            leading: Icon(
              FontAwesomeIcons.fileImage,
              color: Theme.of(context).primaryColor,
            ),
            title: const Text('PDF Main Background'),
            subtitle: Text(
              _selectedBackgroundImage != null
                  ? 'Background image selected'
                  : 'Select a background image for the PDF',
            ),
            trailing:
                _selectedBackgroundImage != null
                    ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            image: DecorationImage(
                              image: FileImage(_selectedBackgroundImage!),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: () {
                            setState(() {
                              _selectedBackgroundImage = null;
                            });
                          },
                        ),
                      ],
                    )
                    : const Icon(FontAwesomeIcons.plus),
            onTap: _pickBackgroundImage,
          ),
        ),

        const SizedBox(height: 16),

        _buildBackgroundTransparencySlider(),

        const SizedBox(height: 16),

        _buildSectionHeader(context, 'PDF Background Images by Position'),

        _buildPositionedBackgroundGrid(_positionedPositions),

        const SizedBox(height: 16),

        _buildSectionHeader(context, 'PDF Background Images by Section'),

        _buildPositionedBackgroundGrid(_sectionPositions),
      ],
    );
  }

  Widget _buildBackgroundTransparencySlider() {
    return Card(
      elevation: 10,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      clipBehavior: Clip.antiAlias,
      color: Colors.white,
      shadowColor: Colors.black.withAlpha(80),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  FontAwesomeIcons.circleHalfStroke,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Text(
                  'Main Background Transparency',
                  style: TextStyle(
                    fontSize: FontSizes.labelLarge,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Adjust how transparent or opaque the main background image appears in the PDF',
              style: TextStyle(
                fontSize: FontSizes.bodyMedium,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  'Transparent',
                  style: TextStyle(
                    fontSize: FontSizes.footnote,
                    color: Colors.grey.shade600,
                  ),
                ),
                Expanded(
                  child: Slider(
                    value: _pdfBackgroundTransparency,
                    min: 0.0,
                    max: 1.0,
                    divisions: 20,
                    label: '${(_pdfBackgroundTransparency * 100).round()}%',
                    onChanged: (value) {
                      setState(() {
                        _pdfBackgroundTransparency = value;
                      });
                    },
                  ),
                ),
                Text(
                  'Opaque',
                  style: TextStyle(
                    fontSize: FontSizes.footnote,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Center(
              child: Text(
                'Current: ${(_pdfBackgroundTransparency * 100).round()}%',
                style: TextStyle(
                  fontSize: FontSizes.bodyMedium,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showTransparencyDialog(String position) async {
    final currentTransparency =
        _positionedBackgroundImageTransparency[position] ?? 1.0;
    double newTransparency = currentTransparency;

    final result = await showDialog<double>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(
                'Adjust Transparency',
                style: TextStyle(
                  fontSize: FontSizes.labelLarge,
                  fontWeight: FontWeight.w600,
                ),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Position: ${position.replaceAll('-', ' ').split(' ').map((word) => word[0].toUpperCase() + word.substring(1)).join(' ')}',
                    style: TextStyle(
                      fontSize: FontSizes.bodyMedium,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Text(
                        'Transparent',
                        style: TextStyle(
                          fontSize: FontSizes.footnote,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      Expanded(
                        child: Slider(
                          value: newTransparency,
                          min: 0.0,
                          max: 1.0,
                          divisions: 20,
                          label: '${(newTransparency * 100).round()}%',
                          onChanged: (value) {
                            setDialogState(() {
                              newTransparency = value;
                            });
                          },
                        ),
                      ),
                      Text(
                        'Opaque',
                        style: TextStyle(
                          fontSize: FontSizes.footnote,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Current: ${(newTransparency * 100).round()}%',
                    style: TextStyle(
                      fontSize: FontSizes.bodyMedium,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(newTransparency),
                  child: const Text('Apply'),
                ),
              ],
            );
          },
        );
      },
    );

    if (result != null) {
      setState(() {
        _positionedBackgroundImageTransparency[position] = result;
      });
    }
  }

  Widget _buildPositionedBackgroundGrid(List<String> positions) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
      ),
      padding: EdgeInsets.zero,
      itemCount: positions.length,
      itemBuilder: (context, index) {
        return _buildPositionedBackgroundCard(positions[index]);
      },
    );
  }

  Widget _buildPositionedBackgroundCard(String position) {
    final displayPosition = position
        .replaceAll('-', ' ')
        .split(' ')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');

    IconData getPositionIcon() {
      if (position == 'top-left') return FontAwesomeIcons.anglesUp;
      if (position == 'top-right') return FontAwesomeIcons.angleUp;
      if (position == 'bottom-left') return FontAwesomeIcons.anglesDown;
      if (position == 'bottom-right') return FontAwesomeIcons.angleDown;

      if (position == 'header') return FontAwesomeIcons.heading;
      if (position == 'patient-data') return FontAwesomeIcons.userDoctor;
      if (position == 'echo-parameters') return FontAwesomeIcons.tableList;
      if (position == 'interpretation') return FontAwesomeIcons.fileLines;
      return FontAwesomeIcons.rectangleList;
    }

    final bool hasBackground =
        _positionedBackgroundImages.containsKey(position) ||
        _positionedBackgroundColors.containsKey(position);

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      clipBehavior: Clip.antiAlias,
      color: Colors.white,
      shadowColor: Colors.black.withAlpha(60),
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 4.0),
      child: InkWell(
        onTap: () => _pickPositionedBackgroundImage(position),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      getPositionIcon(),
                      color: Theme.of(context).primaryColor,
                      size: 16,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      displayPosition,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: FontSizes.bodyMedium,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (!hasBackground)
                      Text(
                        'Tap to add',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: FontSizes.footnote,
                        ),
                      ),
                  ],
                ),
              ),
            ),

            Expanded(
              flex: 4,
              child:
                  hasBackground
                      ? Stack(
                        alignment: Alignment.center,
                        children: [
                          if (_positionedBackgroundImages.containsKey(position))
                            Stack(
                              children: [
                                Container(
                                  height: double.infinity,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: FileImage(
                                        _positionedBackgroundImages[position]!,
                                      ),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                Positioned(
                                  bottom: 4,
                                  left: 4,
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.black.withAlpha(153),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      '${((_positionedBackgroundImageTransparency[position] ?? 1.0) * 100).round()}%',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                Positioned(
                                  bottom: 4,
                                  right: 4,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white.withAlpha(204),
                                      shape: BoxShape.circle,
                                    ),
                                    child: IconButton(
                                      icon: const Icon(
                                        FontAwesomeIcons.circleHalfStroke,
                                        size: 20,
                                      ),
                                      padding: EdgeInsets.zero,
                                      constraints: BoxConstraints(
                                        minWidth: 24,
                                        minHeight: 24,
                                      ),
                                      onPressed:
                                          () =>
                                              _showTransparencyDialog(position),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          else if (_positionedBackgroundColors.containsKey(
                            position,
                          ))
                            Builder(
                              builder: (context) {
                                final colorData =
                                    _positionedBackgroundColors[position]!;
                                final String colorHex =
                                    colorData['color'] as String;
                                final double alpha =
                                    colorData['alpha'] as double;

                                Color colorWithAlpha;
                                try {
                                  if (colorHex.length == 6) {
                                    final int r = int.parse(
                                      colorHex.substring(0, 2),
                                      radix: 16,
                                    );
                                    final int g = int.parse(
                                      colorHex.substring(2, 4),
                                      radix: 16,
                                    );
                                    final int b = int.parse(
                                      colorHex.substring(4, 6),
                                      radix: 16,
                                    );
                                    final int alphaValue =
                                        (alpha * 255).round();

                                    colorWithAlpha = Color.fromARGB(
                                      alphaValue,
                                      r,
                                      g,
                                      b,
                                    );

                                    debugPrint(
                                      'Created color for $position: r=$r, g=$g, b=$b, alpha=$alpha, hex=$colorHex',
                                    );

                                    if (alphaValue < 20) {
                                      colorWithAlpha = Color.fromARGB(
                                        50,
                                        r,
                                        g,
                                        b,
                                      );
                                      debugPrint(
                                        'Alpha too low, increased for display only',
                                      );
                                    }
                                  } else {
                                    colorWithAlpha = Color.fromARGB(
                                      (alpha * 255).round() > 50
                                          ? (alpha * 255).round()
                                          : 50,
                                      30,
                                      144,
                                      255,
                                    );
                                    debugPrint(
                                      'Invalid hex color format: $colorHex, using default blue',
                                    );
                                  }
                                } catch (e) {
                                  colorWithAlpha = Color.fromARGB(
                                    128,
                                    30,
                                    144,
                                    255,
                                  );
                                  debugPrint(
                                    'Error parsing color: $e, using default blue',
                                  );
                                }

                                debugPrint(
                                  'Displaying background color for $position: color=$colorHex, alpha=$alpha, alphaValue=${(alpha * 255).round()}',
                                );

                                return Stack(
                                  children: [
                                    Container(
                                      height: double.infinity,
                                      decoration: BoxDecoration(
                                        color: colorWithAlpha,

                                        border: Border.all(
                                          color: Theme.of(context).primaryColor,
                                          width: 2,
                                        ),
                                      ),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'Color Background',
                                            style: TextStyle(
                                              color:
                                                  colorWithAlpha
                                                              .computeLuminance() >
                                                          0.5
                                                      ? Colors.black
                                                      : Colors.white,
                                              fontWeight: FontWeight.bold,
                                              fontSize: FontSizes.footnote,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            'Opacity: ${(alpha * 100).round()}%',
                                            style: TextStyle(
                                              color:
                                                  colorWithAlpha
                                                              .computeLuminance() >
                                                          0.5
                                                      ? Colors.black
                                                      : Colors.white,
                                              fontSize: 10,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 2),
                                          Text(
                                            '#$colorHex',
                                            style: TextStyle(
                                              color:
                                                  colorWithAlpha
                                                              .computeLuminance() >
                                                          0.5
                                                      ? Colors.black
                                                      : Colors.white,
                                              fontSize: 9,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    Positioned(
                                      right: 4,
                                      bottom: 4,
                                      child: Container(
                                        padding: const EdgeInsets.all(2),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context).primaryColor,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),

                          Positioned(
                            top: 0,
                            right: 0,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white.withAlpha(204),
                                shape: BoxShape.circle,
                              ),
                              child: IconButton(
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.red,
                                  size: 20,
                                ),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(
                                  minWidth: 24,
                                  minHeight: 24,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _positionedBackgroundImages.remove(
                                      position,
                                    );
                                    _positionedBackgroundColors.remove(
                                      position,
                                    );
                                    _positionedBackgroundImageTransparency
                                        .remove(position);
                                  });
                                },
                              ),
                            ),
                          ),
                        ],
                      )
                      : Container(
                        height: double.infinity,
                        color: Colors.grey.shade100,
                        child: const Icon(
                          FontAwesomeIcons.plus,
                          color: Colors.grey,
                          size: 16,
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String label,
    String hint,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 4,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        style: const TextStyle(fontSize: FontSizes.inputText),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: TextStyle(
            color: Theme.of(context).primaryColor,
            fontSize: FontSizes.labelMedium,
          ),
          hintStyle: TextStyle(
            color: Colors.grey.shade500,
            fontSize: FontSizes.helperText,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: Theme.of(context).primaryColor,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageSelection(UserProfile profile, bool isLeftSection) {
    return Card(
      elevation: 10,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      clipBehavior: Clip.antiAlias,
      color: Colors.white,
      shadowColor: Colors.black.withAlpha(80),
      margin: const EdgeInsets.symmetric(vertical: 1, horizontal: 0),
      child: SwitchListTile(
        title: Text(
          'Use Arabic in ${isLeftSection ? 'Left' : 'Right'} Section',
        ),
        subtitle: Text(
          'Toggle between Arabic and English for the ${isLeftSection ? 'left' : 'right'} side of the header',
        ),
        value:
            isLeftSection
                ? profile.useArabicLeftHeader
                : profile.useArabicHeader,
        onChanged: (value) {
          setState(() {
            if (isLeftSection) {
              _profileFuture = Future.value(
                profile.copyWith(useArabicLeftHeader: value),
              );
            } else {
              if (!value && profile.useArabicHeader) {
              } else if (value && !profile.useArabicHeader) {
                if (_doctorNameArController.text.isEmpty) {
                  _doctorNameArController.text = profile.doctorNameAr;
                }
                if (_doctorDegreeArController.text.isEmpty) {
                  _doctorDegreeArController.text = profile.doctorDegreeAr;
                }
                if (_doctorLocationArController.text.isEmpty) {
                  _doctorLocationArController.text = profile.doctorLocationAr;
                }
              }
              _profileFuture = Future.value(
                profile.copyWith(useArabicHeader: value),
              );
            }
          });
        },
      ),
    );
  }

  Widget _buildTextColorSelection() {
    return Column(
      children: [
        _buildColorSelectionTile(
          'Doctor Title Color',
          'Change the color of the doctor name and credentials at the top of the page',
          _doctorTitleColor,
          (color) => setState(() => _doctorTitleColor = color),
        ),
        _buildColorSelectionTile(
          'Report Title Color',
          'Change the color of the "Echocardiogram Report" title',
          _reportTitleColor,
          (color) => setState(() => _reportTitleColor = color),
        ),
        _buildColorSelectionTile(
          'Patient Info Text Color',
          'Change the color of text in the patient information section',
          _patientInfoTextColor,
          (color) => setState(() => _patientInfoTextColor = color),
        ),
        _buildColorSelectionTile(
          'Echo Parameters Text Color',
          'Change the color of text in the echo parameters section',
          _echoParametersTextColor,
          (color) => setState(() => _echoParametersTextColor = color),
        ),
        _buildColorSelectionTile(
          'Interpretation Text Color',
          'Change the color of text in the interpretation section',
          _interpretationTextColor,
          (color) => setState(() => _interpretationTextColor = color),
        ),
        _buildColorSelectionTile(
          'Conclusion Text Color',
          'Change the color of text in the conclusion section',
          _conclusionTextColor,
          (color) => setState(() => _conclusionTextColor = color),
        ),
        _buildColorSelectionTile(
          'Footer Text Color',
          'Change the color of text in the footer section',
          _footerTextColor,
          (color) => setState(() => _footerTextColor = color),
        ),
      ],
    );
  }

  Widget _buildColorSelectionTile(
    String title,
    String subtitle,
    Color currentColor,
    Function(Color) onColorChanged,
  ) {
    return Card(
      elevation: 10,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      clipBehavior: Clip.antiAlias,
      color: Colors.white,
      shadowColor: Colors.black.withAlpha(80),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      child: ListTile(
        leading: Icon(
          FontAwesomeIcons.palette,
          color: Theme.of(context).primaryColor,
        ),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: currentColor,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(26),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
        ),
        onTap: () async {
          await _showColorPicker(title, currentColor, onColorChanged);

          if (mounted) {
            setState(() {
              debugPrint('Refreshing UI after color picker for $title');
            });
          }
        },
      ),
    );
  }

  Widget _buildSaveButton(UserProfile profile) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withAlpha(100),
            blurRadius: 8,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : () => _saveProfile(profile),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1E88E5),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child:
            _isLoading
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2.0,
                    color: Colors.white,
                  ),
                )
                : Text(
                  'Save Profile',
                  style: TextStyle(
                    fontSize: FontSizes.buttonLarge,
                    fontWeight: FontWeight.bold,
                  ),
                ),
      ),
    );
  }

  Color _hexToColor(String hexString) {
    try {
      if (hexString.isEmpty) {
        return Colors.black;
      }

      final String cleanHex =
          hexString.startsWith('#') ? hexString.substring(1) : hexString;

      if (cleanHex.length == 6) {
        final int r = int.parse(cleanHex.substring(0, 2), radix: 16);
        final int g = int.parse(cleanHex.substring(2, 4), radix: 16);
        final int b = int.parse(cleanHex.substring(4, 6), radix: 16);

        final Color color = Color.fromRGBO(r, g, b, 1.0);
        return color;
      } else if (cleanHex.length == 3) {
        final String r = cleanHex.substring(0, 1);
        final String g = cleanHex.substring(1, 2);
        final String b = cleanHex.substring(2, 3);

        final int rValue = int.parse('$r$r', radix: 16);
        final int gValue = int.parse('$g$g', radix: 16);
        final int bValue = int.parse('$b$b', radix: 16);

        final Color color = Color.fromRGBO(rValue, gValue, bValue, 1.0);
        return color;
      }
    } catch (e) {}

    return Colors.black;
  }

  String _colorToHex(Color color) {
    final int r = (color.r * 255).round();
    final int g = (color.g * 255).round();
    final int b = (color.b * 255).round();

    final String rHex = r.toRadixString(16).padLeft(2, '0');
    final String gHex = g.toRadixString(16).padLeft(2, '0');
    final String bHex = b.toRadixString(16).padLeft(2, '0');

    final String hex = '$rHex$gHex$bHex';
    debugPrint('Converting color to hex: r=$r, g=$g, b=$b, hex=$hex');
    return hex;
  }

  Future<void> _showColorPicker(
    String title,
    Color currentColor,
    Function(Color) onColorChanged,
  ) async {
    Color pickerColor = currentColor;
    bool isHexMode = true;

    final TextEditingController hexController = TextEditingController(
      text: _colorToHex(currentColor).toUpperCase(),
    );

    final TextEditingController rController = TextEditingController(
      text: (currentColor.r * 255).round().toString(),
    );

    final TextEditingController gController = TextEditingController(
      text: (currentColor.g * 255).round().toString(),
    );

    final TextEditingController bController = TextEditingController(
      text: (currentColor.b * 255).round().toString(),
    );

    void updateRgbControllers(Color color) {
      rController.text = (color.r * 255).round().toString();
      gController.text = (color.g * 255).round().toString();
      bController.text = (color.b * 255).round().toString();
    }

    void updateHexController(Color color) {
      hexController.text = _colorToHex(color).toUpperCase();
    }

    void updateColorFromHex() {
      try {
        final String hex = hexController.text;
        final Color newColor = _hexToColor(hex);
        pickerColor = newColor;
        updateRgbControllers(newColor);
      } catch (e) {}
    }

    void updateColorFromRgb() {
      try {
        final int r = int.parse(rController.text).clamp(0, 255);
        final int g = int.parse(gController.text).clamp(0, 255);
        final int b = int.parse(bController.text).clamp(0, 255);

        final Color newColor = Color.fromRGBO(r, g, b, 1.0);
        pickerColor = newColor;
        updateHexController(newColor);
      } catch (e) {}
    }

    Future<void> saveColorToStorage(Color selectedColor) async {
      try {
        final currentProfile = await UserProfileService.getUserProfile();

        UserProfile updatedProfile;
        if (title.contains('Doctor Title')) {
          updatedProfile = currentProfile.copyWith(
            doctorTitleColor: _colorToHex(selectedColor),
          );
        } else if (title.contains('Report Title')) {
          updatedProfile = currentProfile.copyWith(
            reportTitleColor: _colorToHex(selectedColor),
          );
        } else if (title.contains('Patient Info')) {
          updatedProfile = currentProfile.copyWith(
            patientInfoTextColor: _colorToHex(selectedColor),
          );
        } else if (title.contains('Echo Parameters')) {
          updatedProfile = currentProfile.copyWith(
            echoParametersTextColor: _colorToHex(selectedColor),
          );
        } else if (title.contains('Interpretation')) {
          updatedProfile = currentProfile.copyWith(
            interpretationTextColor: _colorToHex(selectedColor),
          );
        } else if (title.contains('Conclusion')) {
          updatedProfile = currentProfile.copyWith(
            conclusionTextColor: _colorToHex(selectedColor),
          );
        } else if (title.contains('Footer')) {
          updatedProfile = currentProfile.copyWith(
            footerTextColor: _colorToHex(selectedColor),
          );
        } else {
          return;
        }

        UserProfileService.clearCachedProfile();
        final success = await UserProfileService.saveUserProfile(
          updatedProfile,
        );

        if (success) {
          debugPrint(
            'Text color saved successfully for $title: ${_colorToHex(selectedColor)}',
          );

          if (title.contains('Doctor Title')) {
            _doctorTitleColor = selectedColor;
          } else if (title.contains('Report Title')) {
            _reportTitleColor = selectedColor;
          } else if (title.contains('Patient Info')) {
            _patientInfoTextColor = selectedColor;
          } else if (title.contains('Echo Parameters')) {
            _echoParametersTextColor = selectedColor;
          } else if (title.contains('Interpretation')) {
            _interpretationTextColor = selectedColor;
          } else if (title.contains('Conclusion')) {
            _conclusionTextColor = selectedColor;
          } else if (title.contains('Footer')) {
            _footerTextColor = selectedColor;
          }
        } else {
          debugPrint('Failed to save text color for $title');
        }
      } catch (e) {
        debugPrint('Error saving text color for $title: $e');
      }
    }

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(title),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ColorPicker(
                      pickerColor: pickerColor,
                      onColorChanged: (Color color) {
                        setState(() {
                          pickerColor = color;
                          updateHexController(color);
                          updateRgbControllers(color);
                        });
                      },
                      pickerAreaHeightPercent: 0.8,
                      enableAlpha: false,
                      displayThumbColor: true,
                      labelTypes: const [],
                      paletteType: PaletteType.hsv,
                    ),

                    const SizedBox(height: 16),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                isHexMode = true;
                              });
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              decoration: BoxDecoration(
                                color:
                                    isHexMode
                                        ? Theme.of(context).primaryColor
                                        : Colors.grey.shade200,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(8),
                                  bottomLeft: Radius.circular(8),
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  'HEX',
                                  style: TextStyle(
                                    color:
                                        isHexMode
                                            ? Colors.white
                                            : Colors.black87,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                        Expanded(
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                isHexMode = false;
                              });
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              decoration: BoxDecoration(
                                color:
                                    !isHexMode
                                        ? Theme.of(context).primaryColor
                                        : Colors.grey.shade200,
                                borderRadius: const BorderRadius.only(
                                  topRight: Radius.circular(8),
                                  bottomRight: Radius.circular(8),
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  'RGB',
                                  style: TextStyle(
                                    color:
                                        !isHexMode
                                            ? Colors.white
                                            : Colors.black87,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    if (isHexMode)
                      Row(
                        children: [
                          const Text(
                            '# ',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Expanded(
                            child: TextField(
                              controller: hexController,
                              decoration: const InputDecoration(
                                hintText: 'Enter hex code (e.g., FF0000)',
                                border: OutlineInputBorder(),
                              ),
                              onChanged: (value) {
                                setState(() {
                                  updateColorFromHex();
                                });
                              },
                              maxLength: 6,
                              buildCounter:
                                  (
                                    context, {
                                    required currentLength,
                                    required isFocused,
                                    maxLength,
                                  }) => null,
                            ),
                          ),
                        ],
                      )
                    else
                      Column(
                        children: [
                          Row(
                            children: [
                              const SizedBox(
                                width: 30,
                                child: Text(
                                  'R:',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: TextField(
                                  controller: rController,
                                  decoration: const InputDecoration(
                                    hintText: '0-255',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) {
                                    setState(() {
                                      updateColorFromRgb();
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 8),

                          Row(
                            children: [
                              const SizedBox(
                                width: 30,
                                child: Text(
                                  'G:',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: TextField(
                                  controller: gController,
                                  decoration: const InputDecoration(
                                    hintText: '0-255',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) {
                                    setState(() {
                                      updateColorFromRgb();
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 8),

                          Row(
                            children: [
                              const SizedBox(
                                width: 30,
                                child: Text(
                                  'B:',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: TextField(
                                  controller: bController,
                                  decoration: const InputDecoration(
                                    hintText: '0-255',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) {
                                    setState(() {
                                      updateColorFromRgb();
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),

                    const SizedBox(height: 16),

                    Container(
                      width: double.infinity,
                      height: 40,
                      decoration: BoxDecoration(
                        color: pickerColor,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                    ),
                  ],
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: const Text('Cancel'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: const Text('Select'),
                  onPressed: () async {
                    final navigator = Navigator.of(context);
                    final scaffoldMessenger = ScaffoldMessenger.of(context);

                    onColorChanged(pickerColor);

                    await saveColorToStorage(pickerColor);

                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text('$title saved successfully'),
                          duration: const Duration(seconds: 1),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    }

                    navigator.pop();
                  },
                ),
              ],
            );
          },
        );
      },
    );

    hexController.dispose();
    rController.dispose();
    gController.dispose();
    bController.dispose();
  }

  Widget _buildProfileSlotButtons() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(80),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Profile Slots',
            style: TextStyle(
              fontSize: FontSizes.heading2,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Select a profile slot to load or save to:',
            style: TextStyle(
              fontSize: FontSizes.bodyMedium,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(5, (index) {
              final slotNumber = index + 1;
              final isSelected = _selectedProfileSlot == slotNumber;
              final hasData =
                  _profileSlotsHaveData.length > index
                      ? _profileSlotsHaveData[index]
                      : false;

              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: ElevatedButton(
                    onPressed:
                        _isLoading
                            ? null
                            : () => _switchToProfileSlot(slotNumber),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          isSelected
                              ? const Color(0xFF1E88E5)
                              : hasData
                              ? Colors.green.shade100
                              : Colors.grey.shade200,
                      foregroundColor:
                          isSelected
                              ? Colors.white
                              : hasData
                              ? Colors.green.shade800
                              : Colors.grey.shade600,
                      padding: const EdgeInsets.symmetric(vertical: 12.0),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(
                          color:
                              isSelected
                                  ? const Color(0xFF1E88E5)
                                  : hasData
                                  ? Colors.green.shade300
                                  : Colors.grey.shade300,
                          width: 2,
                        ),
                      ),
                      elevation: isSelected ? 4 : 1,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '$slotNumber',
                          style: TextStyle(
                            fontSize: FontSizes.heading3,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (hasData)
                          Icon(
                            FontAwesomeIcons.solidCircleCheck,
                            size: 12,
                            color:
                                isSelected
                                    ? Colors.white
                                    : Colors.green.shade600,
                          ),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: 8),
          Text(
            'Currently editing: Profile $_selectedProfileSlot',
            style: TextStyle(
              fontSize: FontSizes.footnote,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
